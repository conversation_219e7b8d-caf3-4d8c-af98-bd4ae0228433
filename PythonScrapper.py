#!/usr/bin/env python3
"""
Universal Price Scraper for HVAC Equipment Vendors
Supports: Daikin, SupplyHouse, and Lennox Pros
"""

import asyncio
import os
import json
import sys
import re
from datetime import datetime
from urllib.parse import urlparse
from playwright.async_api import async_playwright

# Configuration for each vendor
VENDOR_CONFIGS = {
    "daikin": {
        "domain": "my.daikincomfort.com",
        "login_url": "https://my.daikincomfort.com/login",
        "signin_selector": "a[href='/login']",
        "price_selectors": [".price", ".product-price", "[data-testid='price']"],
        "cookies_path": "daikin_cookies.json",
        "credentials": {
            "email": "<EMAIL>",
            "password": "M0sley1t"
        }
    },
    "supplyhouse": {
        "domain": "www.supplyhouse.com",
        "login_url": "https://www.supplyhouse.com/login",
        "signin_selector": "a[href='/login']",
        "price_selectors": [".product-price", ".price", ".pricing"],
        "cookies_path": "supplyhouse_cookies.json",
        "credentials": {
            "email": "<EMAIL>",
            "password": "m0sley1t"
        }
    },
    "lennoxpros": {
        "domain": "www.lennoxpros.com",
        "login_url": "https://www.lennoxpros.com/",
        "signin_selector": "#samlSignInLink",
        "price_selectors": [".product-pricing", ".price"],
        "cookies_path": "lennoxpros_cookies.json",
        "credentials": {
            "email": "<EMAIL>",
            "password": "M0sley1t"
        }
    }
}

async def random_delay(min_ms=500, max_ms=2000):
    """Add a random delay to simulate human behavior."""
    import random
    delay = random.uniform(min_ms / 1000, max_ms / 1000)
    await asyncio.sleep(delay)

async def load_session(context, cookies_path):
    """Load saved cookies if they exist."""
    try:
        if os.path.exists(cookies_path):
            with open(cookies_path, 'r') as f:
                cookies = json.load(f)
            await context.add_cookies(cookies)
            print(f"Loaded saved session cookies from {cookies_path}")
            return True
        return False
    except Exception as e:
        print(f"Error loading session: {e}")
        return False

async def save_session(context, cookies_path):
    """Save cookies for future sessions."""
    try:
        cookies = await context.cookies()
        with open(cookies_path, 'w') as f:
            json.dump(cookies, f, indent=2)
        print(f"Cookies saved to {cookies_path}")
        return True
    except Exception as e:
        print(f"Error saving session: {e}")
        return False

async def handle_daikin_login(page, config):
    """Handle Daikin-specific login process."""
    try:
        print("Handling Daikin login...")
        await page.goto(config["login_url"], wait_until="domcontentloaded")
        await random_delay(2000, 3000)
        
        # Fill login form
        await page.fill('input[name="username"]', config["credentials"]["email"])
        await page.fill('input[name="password"]', config["credentials"]["password"])
        await page.click('button[type="submit"]')
        
        await random_delay(3000, 5000)
        return True
    except Exception as e:
        print(f"Daikin login failed: {e}")
        return False

async def handle_supplyhouse_login(page, config):
    """Handle SupplyHouse-specific login process."""
    try:
        print("Handling SupplyHouse login...")
        await page.goto(config["login_url"], wait_until="domcontentloaded")
        await random_delay(2000, 3000)
        
        # Fill login form
        await page.fill('#username', config["credentials"]["email"])
        await page.fill('input[type="password"]', config["credentials"]["password"])
        await page.click('button.btn.btn-lg.btn-block.button-blue[type="submit"]')
        
        await random_delay(3000, 5000)
        return True
    except Exception as e:
        print(f"SupplyHouse login failed: {e}")
        return False

async def handle_lennoxpros_login(page, config):
    """Handle Lennox Pros-specific login process."""
    try:
        print("Handling Lennox Pros login...")
        await page.goto(config["login_url"], wait_until="domcontentloaded")
        await random_delay(2000, 3000)
        
        # Check if already logged in
        signin_link = await page.query_selector(config["signin_selector"])
        if not signin_link:
            print("Already logged in to Lennox Pros")
            return True
        
        # Click sign-in link
        await page.click(config["signin_selector"])
        await random_delay(3000, 5000)
        
        # Fill login form
        await page.fill('#signInName', config["credentials"]["email"])
        await page.fill('#password', config["credentials"]["password"])
        await page.click('#continueProxy')
        
        await random_delay(5000, 8000)
        
        # Handle verification if needed
        verification_field = await page.query_selector('#dummy_verificationCode')
        if verification_field:
            print("Verification code required for Lennox Pros")
            verification_code = input("Enter verification code: ").strip()
            if verification_code:
                await page.fill('#dummy_verificationCode', verification_code)
                await page.click('#dummy_verify_code')
                await random_delay(3000, 5000)
        
        return True
    except Exception as e:
        print(f"Lennox Pros login failed: {e}")
        return False

async def handle_lennoxpros_location_modal(page):
    """Handle Lennox Pros location modal if it appears."""
    try:
        modal_title = await page.wait_for_selector('.translations-modal-title', state="visible", timeout=3000)
        if modal_title:
            print("Handling Lennox Pros location modal...")
            await page.click('#unitedStates')
            await page.click('#remember')
            await page.click('#continueBtn')
            await random_delay(2000, 3000)
            return True
    except:
        return False

async def extract_price(page, vendor, price_selectors):
    """Extract price from the product page."""
    try:
        print(f"Extracting price for {vendor}...")
        
        # Wait for page to load
        await random_delay(2000, 3000)
        
        # Try each price selector
        price = None
        for selector in price_selectors:
            try:
                await page.wait_for_selector(selector, state="visible", timeout=5000)
                price_element = await page.query_selector(selector)
                if price_element:
                    price_text = await price_element.text_content()
                    # Clean up price text
                    price = re.sub(r'[^\d.,]', '', price_text.strip())
                    if price and '$' not in price_text:
                        price = f"${price}"
                    elif '$' in price_text:
                        price = re.search(r'\$[\d,]+\.?\d*', price_text)
                        price = price.group(0) if price else price_text.strip()
                    
                    if price:
                        print(f"Found price using selector '{selector}': {price}")
                        break
            except:
                continue
        
        if not price:
            price = "Price not found"
            print("Could not find price on page")
        
        return price
        
    except Exception as e:
        print(f"Error extracting price: {e}")
        return "Error extracting price"

async def scrape_product_price(url):
    """Main function to scrape product price from any supported vendor."""
    
    # Determine vendor from URL
    parsed_url = urlparse(url)
    domain = parsed_url.netloc
    
    vendor = None
    config = None
    
    for vendor_name, vendor_config in VENDOR_CONFIGS.items():
        if vendor_config["domain"] in domain:
            vendor = vendor_name
            config = vendor_config
            break
    
    if not vendor:
        return {
            "error": f"Unsupported domain: {domain}",
            "supported_domains": list(VENDOR_CONFIGS.keys())
        }
    
    print(f"Detected vendor: {vendor}")
    print(f"Product URL: {url}")
    
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu'
            ]
        )
        
        context = await browser.new_context(
            viewport={"width": 1280, "height": 800},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        try:
            # Load existing session
            session_loaded = await load_session(context, config["cookies_path"])
            
            page = await context.new_page()
            
            # Navigate to product page first to check if login is needed
            print(f"Navigating to product page...")
            await page.goto(url, wait_until="domcontentloaded", timeout=60000)
            await random_delay(2000, 3000)
            
            # Check if we need to login (look for login indicators)
            needs_login = False
            
            if vendor == "daikin":
                login_indicator = await page.query_selector('a[href="/login"]')
                needs_login = bool(login_indicator)
            elif vendor == "supplyhouse":
                login_indicator = await page.query_selector('a[href="/login"]')
                needs_login = bool(login_indicator)
            elif vendor == "lennoxpros":
                login_indicator = await page.query_selector('#samlSignInLink')
                needs_login = bool(login_indicator)
            
            # Login if needed
            if needs_login and not session_loaded:
                print(f"Login required for {vendor}")
                
                if vendor == "daikin":
                    login_success = await handle_daikin_login(page, config)
                elif vendor == "supplyhouse":
                    login_success = await handle_supplyhouse_login(page, config)
                elif vendor == "lennoxpros":
                    login_success = await handle_lennoxpros_login(page, config)
                
                if login_success:
                    await save_session(context, config["cookies_path"])
                    # Navigate back to product page
                    await page.goto(url, wait_until="domcontentloaded", timeout=60000)
                    await random_delay(2000, 3000)
                else:
                    return {"error": f"Login failed for {vendor}"}
            
            # Handle vendor-specific modals
            if vendor == "lennoxpros":
                await handle_lennoxpros_location_modal(page)
            
            # Extract product information
            title_element = await page.query_selector('h1')
            title = await title_element.text_content() if title_element else "Product title not found"
            title = title.strip()
            
            # Extract price
            price = await extract_price(page, vendor, config["price_selectors"])
            
            # Compile result
            result = {
                "vendor": vendor,
                "title": title,
                "price": price,
                "url": url,
                "scraped_at": datetime.now().isoformat(),
                "success": price != "Price not found" and "Error" not in price
            }
            
            return result
            
        except Exception as e:
            return {
                "error": f"Scraping failed: {str(e)}",
                "vendor": vendor,
                "url": url
            }
        
        finally:
            await browser.close()

def main():
    """CLI interface for the universal price scraper."""
    if len(sys.argv) != 2:
        print("Usage: python3 universal_price_scraper.py <product_url>")
        print("\nSupported vendors:")
        for vendor, config in VENDOR_CONFIGS.items():
            print(f"  - {vendor.title()}: {config['domain']}")
        sys.exit(1)
    
    url = sys.argv[1]
    
    # Run the scraper
    result = asyncio.run(scrape_product_price(url))
    
    # Output result as JSON
    print(json.dumps(result, indent=2))
    
    # Save to file
    os.makedirs("scraped_data", exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"scraped_data/universal_scrape_{timestamp}.json"
    with open(filename, 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\nResult saved to: {filename}")

if __name__ == "__main__":
    main()
