package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"

	"go-scrapper/internal/config"
	"go-scrapper/internal/scraper"
)

func main() {
	// Check command line arguments
	if len(os.Args) != 2 {
		printUsage()
		os.Exit(1)
	}
	
	productURL := os.Args[1]
	
	// Run the scraper
	result, err := scraper.ScrapeProductPrice(productURL)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		os.Exit(1)
	}
	
	// Output result as JSON
	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling result: %v\n", err)
		os.Exit(1)
	}
	
	fmt.Println(string(jsonData))
	
	// Save to file
	err = os.MkdirAll("scraped_data", 0755)
	if err != nil {
		fmt.Printf("Warning: failed to create scraped_data directory: %v\n", err)
	} else {
		timestamp := time.Now().Format("20060102_150405")
		filename := fmt.Sprintf("scraped_data/universal_scrape_%s.json", timestamp)
		
		file, err := os.Create(filename)
		if err != nil {
			fmt.Printf("Warning: failed to create result file: %v\n", err)
		} else {
			defer file.Close()
			
			encoder := json.NewEncoder(file)
			encoder.SetIndent("", "  ")
			if err := encoder.Encode(result); err != nil {
				fmt.Printf("Warning: failed to write result file: %v\n", err)
			} else {
				fmt.Printf("\nResult saved to: %s\n", filename)
			}
		}
	}
}

func printUsage() {
	fmt.Println("Usage: go run main.go <product_url>")
	fmt.Println("\nSupported vendors:")
	
	for vendorName, vendorConfig := range config.VendorConfigs {
		fmt.Printf("  - %s: %s\n", 
			capitalizeFirst(vendorName), 
			vendorConfig.Domain)
	}
}

func capitalizeFirst(s string) string {
	if len(s) == 0 {
		return s
	}
	return string(s[0]-32) + s[1:]
}
