package models

import "time"

// VendorCredentials holds login credentials for a vendor
type VendorCredentials struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// VendorConfig holds configuration for a specific vendor
type VendorConfig struct {
	Domain         string            `json:"domain"`
	LoginURL       string            `json:"login_url"`
	SigninSelector string            `json:"signin_selector"`
	PriceSelectors []string          `json:"price_selectors"`
	CookiesPath    string            `json:"cookies_path"`
	Credentials    VendorCredentials `json:"credentials"`
}

// ScrapeResult holds the result of a scraping operation
type ScrapeResult struct {
	Vendor    string    `json:"vendor"`
	Title     string    `json:"title"`
	Price     string    `json:"price"`
	URL       string    `json:"url"`
	ScrapedAt time.Time `json:"scraped_at"`
	Success   bool      `json:"success"`
	Error     string    `json:"error,omitempty"`
}

// CookieData represents saved cookie information
type CookieData struct {
	Name     string `json:"name"`
	Value    string `json:"value"`
	Domain   string `json:"domain"`
	Path     string `json:"path"`
	Expires  int64  `json:"expires"`
	HttpOnly bool   `json:"httpOnly"`
	Secure   bool   `json:"secure"`
}
