# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Built binaries
scraper
go-scrapper
main

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Scraped data and session files
scraped_data/
*.json
*_cookies.json
daikin_cookies.json
supplyhouse_cookies.json
lennoxpros_cookies.json

# Environment files
.env
.env.local
.env.*.local

# Temporary files
tmp/
temp/
*.tmp

# Coverage reports
coverage.txt
coverage.html

# Playwright browsers (optional - uncomment if you want to ignore)
# These are large and can be reinstalled
# ~/.cache/ms-playwright/
# ms-playwright/

# Debug files
debug
*.debug

# Air live reload tool
tmp/

# Local configuration files
config.local.json
settings.local.json

# Backup files
*.bak
*.backup

# Node modules (if using any JS tools)
node_modules/

# Python files (from original script)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Security - never commit credentials
credentials.json
secrets.json
*.key
*.pem
*.crt
