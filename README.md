# Go HVAC Equipment Price Scraper

A Go-based universal price scraper for HVAC equipment vendors, converted from the original Python version. This tool supports automated price extraction from Daikin, SupplyHouse, and Lennox Pros websites.

## Features

- **Multi-vendor support**: Daikin, SupplyHouse, and Lennox Pros
- **Automated login**: Handles vendor-specific authentication flows
- **Session management**: Saves and reuses browser cookies
- **Human-like behavior**: Random delays to avoid detection
- **Robust price extraction**: Multiple CSS selectors per vendor
- **JSON output**: Structured results with timestamps
- **Error handling**: Comprehensive error reporting

## Prerequisites

1. **Go 1.22+**: Make sure Go is installed on your system
2. **Playwright browsers**: Install Playwright browser binaries

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   go mod tidy
   ```
3. Install Playwright browsers:
   ```bash
   go run github.com/playwright-community/playwright-go/cmd/playwright@latest install
   ```

## Usage

### Basic Usage

```bash
go run main.go <product_url>
```

### Examples

```bash
# Scrape a Daikin product
go run main.go "https://my.daikincomfort.com/product/example"

# Scrape a SupplyHouse product  
go run main.go "https://www.supplyhouse.com/product/example"

# Scrape a Lennox Pros product
go run main.go "https://www.lennoxpros.com/product/example"
```

### Building

To build a standalone executable:

```bash
go build -o scraper main.go
./scraper <product_url>
```

## Output

The scraper outputs JSON results both to stdout and saves them to the `scraped_data/` directory:

```json
{
  "vendor": "daikin",
  "title": "Product Name",
  "price": "$1,234.56",
  "url": "https://example.com/product",
  "scraped_at": "2024-01-15T10:30:00Z",
  "success": true
}
```

## Configuration

Vendor configurations are stored in `internal/config/config.go`. Each vendor has:

- **Domain**: The website domain to match
- **Login URL**: Where to authenticate
- **Selectors**: CSS selectors for login elements and prices
- **Credentials**: Login email and password
- **Cookie storage**: Path for session persistence

## Project Structure

```
go-scrapper/
├── main.go                    # CLI entry point
├── go.mod                     # Go module definition
├── pkg/models/               # Data structures
│   └── models.go
├── internal/
│   ├── config/               # Vendor configurations
│   │   └── config.go
│   ├── scraper/              # Main scraping logic
│   │   └── scraper.go
│   ├── vendors/              # Vendor-specific handlers
│   │   └── vendors.go
│   ├── session/              # Cookie/session management
│   │   └── session.go
│   └── utils/                # Utility functions
│       └── utils.go
└── scraped_data/             # Output directory (created automatically)
```

## Supported Vendors

| Vendor | Domain | Features |
|--------|--------|----------|
| Daikin | my.daikincomfort.com | Standard login, price extraction |
| SupplyHouse | www.supplyhouse.com | Standard login, price extraction |
| Lennox Pros | www.lennoxpros.com | SAML login, 2FA support, location modal |

## Error Handling

The scraper handles various error conditions:

- **Unsupported domains**: Returns error with list of supported vendors
- **Login failures**: Detailed error messages for authentication issues
- **Network timeouts**: Configurable timeouts for page loads
- **Missing elements**: Graceful handling when selectors don't match
- **Price extraction failures**: Falls back through multiple selectors

## Security Notes

- Credentials are stored in plain text in the configuration
- Consider using environment variables for production use
- Session cookies are saved locally for reuse
- The scraper uses a realistic User-Agent string

## Differences from Python Version

- **Synchronous execution**: Go version doesn't use async/await patterns
- **Strong typing**: Struct-based configuration instead of dictionaries  
- **Better error handling**: Go's explicit error handling vs Python exceptions
- **Compiled binary**: Can be distributed as a single executable
- **Memory efficiency**: Generally lower memory usage than Python

## Troubleshooting

### Common Issues

1. **Playwright not installed**: Run the browser installation command
2. **Login failures**: Check credentials in config file
3. **Price not found**: Website may have changed selectors
4. **Timeout errors**: Increase timeout values or check network

### Debug Mode

To run with more verbose output, you can modify the scraper to run in non-headless mode by changing `Headless: playwright.Bool(false)` in `internal/scraper/scraper.go`.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is provided as-is for educational and personal use.
