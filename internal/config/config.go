package config

import (
	"go-scrapper/pkg/models"
	"strings"
)

// VendorConfigs holds configuration for all supported vendors
var VendorConfigs = map[string]models.VendorConfig{
	"daikin": {
		Domain:         "my.daikincomfort.com",
		LoginURL:       "https://my.daikincomfort.com/login",
		SigninSelector: "a[href='/login']",
		PriceSelectors: []string{".price", ".product-price", "[data-testid='price']"},
		CookiesPath:    "daikin_cookies.json",
		Credentials: models.VendorCredentials{
			Email:    "<EMAIL>",
			Password: "M0sley1t",
		},
	},
	"supplyhouse": {
		Domain:         "www.supplyhouse.com",
		LoginURL:       "https://www.supplyhouse.com/login",
		SigninSelector: "a[href='/login']",
		PriceSelectors: []string{".product-price", ".price", ".pricing"},
		CookiesPath:    "supplyhouse_cookies.json",
		Credentials: models.VendorCredentials{
			Email:    "<EMAIL>",
			Password: "m0sley1t",
		},
	},
	"lennoxpros": {
		Domain:         "www.lennoxpros.com",
		LoginURL:       "https://www.lennoxpros.com/",
		SigninSelector: "#samlSignInLink",
		PriceSelectors: []string{".product-pricing", ".price"},
		CookiesPath:    "lennoxpros_cookies.json",
		Credentials: models.VendorCredentials{
			Email:    "<EMAIL>",
			Password: "M0sley1t",
		},
	},
}

// GetSupportedDomains returns a list of all supported vendor domains
func GetSupportedDomains() []string {
	domains := make([]string, 0, len(VendorConfigs))
	for _, config := range VendorConfigs {
		domains = append(domains, config.Domain)
	}
	return domains
}

// GetVendorByDomain returns the vendor name and config for a given domain
func GetVendorByDomain(domain string) (string, models.VendorConfig, bool) {
	for vendorName, config := range VendorConfigs {
		configDomain := config.Domain
		// Strip www. from config domain for comparison
		if strings.HasPrefix(configDomain, "www.") {
			configDomain = configDomain[4:]
		}

		// Strip www. from input domain for comparison
		inputDomain := domain
		if strings.HasPrefix(inputDomain, "www.") {
			inputDomain = inputDomain[4:]
		}

		if configDomain == inputDomain {
			return vendorName, config, true
		}
	}
	return "", models.VendorConfig{}, false
}
