package vendors

import (
	"bufio"
	"fmt"
	"os"
	"strings"

	"go-scrapper/internal/utils"
	"go-scrapper/pkg/models"

	"github.com/playwright-community/playwright-go"
)

// HandleDaikinLogin handles Daikin-specific login process
func HandleDaikinLogin(page playwright.Page, config models.VendorConfig) error {
	fmt.Println("Handling Daikin login...")

	_, err := page.Goto(config.LoginUR<PERSON>, playwright.PageGotoOptions{
		WaitUntil: playwright.WaitUntilStateDomcontentloaded,
	})
	if err != nil {
		return fmt.Errorf("failed to navigate to login page: %w", err)
	}

	utils.RandomDelay(2000, 3000)

	// Fill login form
	err = page.Fill("input[name=\"username\"]", config.Credentials.Email)
	if err != nil {
		return fmt.Errorf("failed to fill username: %w", err)
	}

	err = page.Fill("input[name=\"password\"]", config.Credentials.Password)
	if err != nil {
		return fmt.Errorf("failed to fill password: %w", err)
	}

	err = page.Click("button[type=\"submit\"]")
	if err != nil {
		return fmt.Errorf("failed to click submit button: %w", err)
	}

	utils.RandomDelay(3000, 5000)
	return nil
}

// HandleSupplyHouseLogin handles SupplyHouse-specific login process
func HandleSupplyHouseLogin(page playwright.Page, config models.VendorConfig) error {
	fmt.Println("Handling SupplyHouse login...")

	_, err := page.Goto(config.LoginURL, playwright.PageGotoOptions{
		WaitUntil: playwright.WaitUntilStateDomcontentloaded,
	})
	if err != nil {
		return fmt.Errorf("failed to navigate to login page: %w", err)
	}

	utils.RandomDelay(2000, 3000)

	// Fill login form
	err = page.Fill("#username", config.Credentials.Email)
	if err != nil {
		return fmt.Errorf("failed to fill username: %w", err)
	}

	err = page.Fill("input[type=\"password\"]", config.Credentials.Password)
	if err != nil {
		return fmt.Errorf("failed to fill password: %w", err)
	}

	err = page.Click("button.btn.btn-lg.btn-block.button-blue[type=\"submit\"]")
	if err != nil {
		return fmt.Errorf("failed to click submit button: %w", err)
	}

	utils.RandomDelay(3000, 5000)
	return nil
}

// HandleLennoxProsLogin handles Lennox Pros-specific login process
func HandleLennoxProsLogin(page playwright.Page, config models.VendorConfig) error {
	fmt.Println("Handling Lennox Pros login...")

	_, err := page.Goto(config.LoginURL, playwright.PageGotoOptions{
		WaitUntil: playwright.WaitUntilStateDomcontentloaded,
	})
	if err != nil {
		return fmt.Errorf("failed to navigate to login page: %w", err)
	}

	utils.RandomDelay(2000, 3000)

	// Check if already logged in
	signinElement := page.Locator(config.SigninSelector).First()
	if signinElement != nil {
		visible, err := signinElement.IsVisible()
		if err == nil && !visible {
			fmt.Println("Already logged in to Lennox Pros")
			return nil
		}
	}

	// Click sign-in link
	err = page.Click(config.SigninSelector)
	if err != nil {
		return fmt.Errorf("failed to click sign-in link: %w", err)
	}

	utils.RandomDelay(3000, 5000)

	// Fill login form
	err = page.Fill("#signInName", config.Credentials.Email)
	if err != nil {
		return fmt.Errorf("failed to fill username: %w", err)
	}

	err = page.Fill("#password", config.Credentials.Password)
	if err != nil {
		return fmt.Errorf("failed to fill password: %w", err)
	}

	err = page.Click("#continueProxy")
	if err != nil {
		return fmt.Errorf("failed to click continue button: %w", err)
	}

	utils.RandomDelay(5000, 8000)

	// Handle verification if needed
	verificationElement := page.Locator("#dummy_verificationCode").First()
	if verificationElement != nil {
		visible, err := verificationElement.IsVisible()
		if err == nil && visible {
			fmt.Println("Verification code required for Lennox Pros")
			fmt.Print("Enter verification code: ")

			reader := bufio.NewReader(os.Stdin)
			verificationCode, err := reader.ReadString('\n')
			if err != nil {
				return fmt.Errorf("failed to read verification code: %w", err)
			}

			verificationCode = strings.TrimSpace(verificationCode)
			if verificationCode != "" {
				err = page.Fill("#dummy_verificationCode", verificationCode)
				if err != nil {
					return fmt.Errorf("failed to fill verification code: %w", err)
				}

				err = page.Click("#dummy_verify_code")
				if err != nil {
					return fmt.Errorf("failed to click verify button: %w", err)
				}

				utils.RandomDelay(3000, 5000)
			}
		}
	}

	return nil
}

// HandleLennoxProsLocationModal handles Lennox Pros location modal if it appears
func HandleLennoxProsLocationModal(page playwright.Page) error {
	// Wait for modal with timeout
	_, err := page.WaitForSelector(".translations-modal-title", playwright.PageWaitForSelectorOptions{
		State:   playwright.WaitForSelectorStateVisible,
		Timeout: playwright.Float(3000),
	})
	if err != nil {
		// Modal didn't appear, which is fine
		return nil
	}

	fmt.Println("Handling Lennox Pros location modal...")

	err = page.Click("#unitedStates")
	if err != nil {
		return fmt.Errorf("failed to click United States: %w", err)
	}

	err = page.Click("#remember")
	if err != nil {
		return fmt.Errorf("failed to click remember: %w", err)
	}

	err = page.Click("#continueBtn")
	if err != nil {
		return fmt.Errorf("failed to click continue: %w", err)
	}

	utils.RandomDelay(2000, 3000)
	return nil
}
