package utils

import (
	"fmt"
	"math/rand"
	"regexp"
	"strings"
	"time"

	"github.com/playwright-community/playwright-go"
)

// RandomDelay adds a random delay to simulate human behavior
func RandomDelay(minMs, maxMs int) {
	delay := time.Duration(rand.Intn(maxMs-minMs)+minMs) * time.Millisecond
	time.Sleep(delay)
}

// ExtractPrice extracts price from the product page using multiple selectors
func ExtractPrice(page playwright.Page, vendor string, priceSelectors []string) (string, error) {
	fmt.Printf("Extracting price for %s...\n", vendor)

	// Wait for page to load
	RandomDelay(2000, 3000)

	// Try each price selector
	for _, selector := range priceSelectors {
		// Wait for selector with timeout
		_, err := page.WaitForSelector(selector, playwright.PageWaitForSelectorOptions{
			State:   playwright.WaitForSelectorStateVisible,
			Timeout: playwright.Float(5000),
		})
		if err != nil {
			continue
		}

		element := page.Locator(selector).First()
		if element == nil {
			continue
		}

		priceText, err := element.TextContent()
		if err != nil {
			continue
		}

		// Clean up price text
		priceText = strings.TrimSpace(priceText)
		if priceText == "" {
			continue
		}

		// Extract price using regex
		var price string
		if strings.Contains(priceText, "$") {
			// If already contains $, extract the price pattern
			re := regexp.MustCompile(`\$[\d,]+\.?\d*`)
			match := re.FindString(priceText)
			if match != "" {
				price = match
			} else {
				price = priceText
			}
		} else {
			// Remove non-numeric characters except dots and commas
			re := regexp.MustCompile(`[^\d.,]`)
			cleanPrice := re.ReplaceAllString(priceText, "")
			if cleanPrice != "" {
				price = "$" + cleanPrice
			}
		}

		if price != "" {
			fmt.Printf("Found price using selector '%s': %s\n", selector, price)
			return price, nil
		}
	}

	// Take a screenshot for debugging when price is not found
	timestamp := time.Now().Format("20060102_150405")
	screenshotPath := fmt.Sprintf("debug_screenshot_%s_%s.png", vendor, timestamp)
	_, err := page.Screenshot(playwright.PageScreenshotOptions{
		Path: playwright.String(screenshotPath),
	})
	if err != nil {
		fmt.Printf("Warning: failed to take debug screenshot: %v\n", err)
	} else {
		fmt.Printf("Debug screenshot saved to: %s\n", screenshotPath)
	}

	return "Price not found", fmt.Errorf("could not find price on page")
}

// GetProductTitle extracts the product title from the page
func GetProductTitle(page playwright.Page) string {
	element := page.Locator("h1").First()
	if element == nil {
		return "Product title not found"
	}

	title, err := element.TextContent()
	if err != nil {
		return "Product title not found"
	}

	return strings.TrimSpace(title)
}

// CheckLoginRequired checks if login is required for the given vendor
func CheckLoginRequired(page playwright.Page, vendor string) (bool, error) {
	var loginSelector string

	switch vendor {
	case "daikin", "supplyhouse":
		loginSelector = "a[href='/login']"
	case "lennoxpros":
		loginSelector = "#samlSignInLink"
	default:
		return false, fmt.Errorf("unknown vendor: %s", vendor)
	}

	element := page.Locator(loginSelector).First()
	if element == nil {
		return false, nil
	}

	// Check if element is visible
	visible, err := element.IsVisible()
	if err != nil {
		return false, err
	}

	return visible, nil
}
